/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Cinzel+Decorative:wght@400;700;900&display=swap');

/* Modern CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for Modern Design */
:root {
    --primary-color: #0066cc;
    --secondary-color: #004499;
    --accent-color: #00aaff;
    --dark-color: #1a1a1a;
    --light-color: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --success-color: #25d366;
    --border-radius: 12px;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.2);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Hero */
        --hero-primary-color: #202020;
    --hero-secondary-color: #000000;
}

/* Base Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--light-color);
    font-size: 16px;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: var(--transition);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    font-family: 'Cinzel Decorative', serif;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 0.05em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

nav a {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: var(--transition);
    position: relative;
}

nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background: var(--primary-color);
    transition: var(--transition);
}

nav a:hover {
    color: var(--primary-color);
}

nav a:hover::after {
    width: 100%;
}

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-700);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition);
}

.menu-toggle:hover {
    background: var(--gray-100);
}

/* Main Content */
main {
    margin-top: 80px;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--hero-primary-color) 0%, var(--hero-secondary-color) 100%);
    color: var(--light-color);
    padding: 120px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('photos/metal.jpg') center/cover;
    opacity: 0.2;
    z-index: 0;
}

.hero .container {
    position: relative;
    z-index: 1;
}

.hero h2 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    margin-bottom: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    line-height: 1.1;
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.95;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background: var(--light-color);
    color: var(--primary-color);
    padding: 16px 32px;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--gray-100);
}

/* Section Styles */
.section {
    padding: 100px 0;
}

.section-title {
    text-align: center;
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 4rem;
    color: var(--gray-900);
    font-weight: 700;
    letter-spacing: -0.025em;
}

/* Services Section */
.services {
    background: var(--gray-100);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-card {
    background: var(--light-color);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.service-image {
    width: 100%;
    height: 220px;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    object-fit: cover;
    transition: var(--transition);
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--gray-900);
    font-weight: 600;
}

.service-card p {
    color: var(--gray-600);
    line-height: 1.7;
    font-size: 1rem;
}

/* About Section */
.about {
    background: var(--light-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    font-size: 1.1rem;
    line-height: 1.8;
}

.about-text p {
    margin-bottom: 1.5rem;
    color: var(--gray-700);
}

.about-image-container {
    position: relative;
}

.about-image {
    width: 100%;
    height: 400px;
    border-radius: var(--border-radius);
    object-fit: cover;
    box-shadow: var(--shadow-md);
}

/* Footer */
footer {
    background: var(--gray-900);
    color: var(--light-color);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
}

.footer-section h3 {
    margin-bottom: 1.5rem;
    color: var(--accent-color);
    font-weight: 600;
    font-size: 1.25rem;
}

.footer-section p, .footer-section a {
    color: var(--gray-300);
    text-decoration: none;
    margin-bottom: 0.75rem;
    display: block;
    line-height: 1.6;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--accent-color);
}

.footer-bottom {
    text-align: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-700);
    color: var(--gray-400);
}

/* WhatsApp Button */
.whatsapp-float {
    position: fixed;
    width: 64px;
    height: 64px;
    bottom: 32px;
    right: 32px;
    background: var(--success-color);
    color: var(--light-color);
    border-radius: 50%;
    text-align: center;
    font-size: 28px;
    box-shadow: var(--shadow-lg);
    z-index: 100;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.whatsapp-float:hover {
    background: #128c7e;
    transform: scale(1.1);
    box-shadow: 0 12px 30px rgba(37, 211, 102, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .menu-toggle {
        display: block;
    }

    nav ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--light-color);
        flex-direction: column;
        padding: 1rem;
        border-top: 1px solid var(--gray-200);
        box-shadow: var(--shadow-md);
    }

    nav ul.active {
        display: flex;
    }

    .hero {
        padding: 80px 0;
    }

    .section {
        padding: 60px 0;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .whatsapp-float {
        width: 56px;
        height: 56px;
        bottom: 24px;
        right: 24px;
        font-size: 24px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}
